luaExecutor.ts:70 === SENDING TO RUST ===
luaExecutor.ts:71 Script content: -- Simple test to verify boolean operations are working
-- Creates one large, obvious cut that should be clearly visible

-- Set up basic variables
X = 200  -- Small door for testing
Y = 150
materialThickness = 18

function modelMain()
    print("=== SIMPLE CUT TEST ===")
    
    -- Initialize ADekoLib
    G = ADekoLib
    
    -- Create door panel
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()
    print("✓ Small door panel created (200x150mm)")
    
    -- Create ONE LARGE, OBVIOUS CUT
    G.setFace("top")
    G.setLayer("50MM")  -- Very large tool
    G.setThickness(-15)  -- Very deep cut (almost through the door)
    
    -- Create a large rectangle that takes up most of the door
    G.rectangle({25, 25}, {175, 125})
    print("✓ LARGE CUT: 50mm tool, 15mm deep, covers most of door surface")
    print("✓ Cut area: (25,25) to (175,125) - should be VERY visible")
    
    print("=== SIMPLE CUT TEST COMPLETED ===")
    print("This should create a MASSIVE rectangular pocket in the door")
    print("If you can't see this cut in 3D wireframe, there's a fundamental issue")
    
    return true
end

require "ADekoDebugMode"

luaExecutor.ts:72 Lua library path: ./LIBRARY\luaLibrary
luaExecutor.ts:73 Debug mode: false
luaExecutor.ts:74 === END SENDING TO RUST ===
App.vue:690 Full result object: {
  "success": true,
  "output": "DEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is table: 0000012974CB51D0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012974CB51D0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012974CB51D0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012974CB51D0\nDEBUG: Engine is available, proceeding with engine calls\n=== SIMPLE CUT TEST ===\n✓ Small door panel created (200x150mm)\n✓ LARGE CUT: 50mm tool, 15mm deep, covers most of door surface\n✓ Cut area: (25,25) to (175,125) - should be VERY visible\n=== SIMPLE CUT TEST COMPLETED ===\nThis should create a MASSIVE rectangular pocket in the door\nIf you can't see this cut in 3D wireframe, there's a fundamental issue\nDebug model JSON generated:\n{\n  \"models\": {\n    \"debug\": {\n      \"paths\": []\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-06 08:25:53\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nGeometry model JSON generated:\n{\n  \"models\": {\n    \"50MM\": {\n      \"paths\": {\n        \"rect_bottom_9\": {\n          \"end\": [\n            65.0,\n            221.0\n          ],\n          \"origin\": [\n            215.0,\n            221.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_12\": {\n          \"end\": [\n            65.0,\n            121.0\n          ],\n          \"origin\": [\n            65.0,\n            221.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_11\": {\n          \"end\": [\n            215.0,\n            221.0\n          ],\n          \"origin\": [\n            215.0,\n            121.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_10\": {\n          \"end\": [\n            215.0,\n            121.0\n          ],\n          \"origin\": [\n            65.0,\n            121.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM0\": {\n      \"paths\": {\n        \"line_1\": {\n          \"end\": [\n            500.0,\n            0.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM1\": {\n      \"paths\": {\n        \"line_2\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            700.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM2\": {\n      \"paths\": {\n        \"line_3\": {\n          \"end\": [\n            0.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM3\": {\n      \"paths\": {\n        \"line_4\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            500.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"PANEL\": {\n      \"paths\": {\n        \"rect_bottom_5\": {\n          \"end\": [\n            40.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_8\": {\n          \"end\": [\n            40.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_7\": {\n          \"end\": [\n            540.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_6\": {\n          \"end\": [\n            540.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-06 08:25:53\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nDEBUG: modelMain function found in global scope\nDEBUG: Script execution time: 5ms\nDEBUG: Draw commands generated: 0",
  "error": null,
  "draw_commands": [],
  "execution_time_ms": 5,
  "debug_state": null,
  "makerjs_json": "{\n  \"models\": {\n    \"50MM\": {\n      \"paths\": {\n        \"rect_bottom_9\": {\n          \"end\": [\n            65.0,\n            221.0\n          ],\n          \"origin\": [\n            215.0,\n            221.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_12\": {\n          \"end\": [\n            65.0,\n            121.0\n          ],\n          \"origin\": [\n            65.0,\n            221.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_11\": {\n          \"end\": [\n            215.0,\n            221.0\n          ],\n          \"origin\": [\n            215.0,\n            121.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_10\": {\n          \"end\": [\n            215.0,\n            121.0\n          ],\n          \"origin\": [\n            65.0,\n            121.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM0\": {\n      \"paths\": {\n        \"line_1\": {\n          \"end\": [\n            500.0,\n            0.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM1\": {\n      \"paths\": {\n        \"line_2\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            700.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM2\": {\n      \"paths\": {\n        \"line_3\": {\n          \"end\": [\n            0.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM3\": {\n      \"paths\": {\n        \"line_4\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            500.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"PANEL\": {\n      \"paths\": {\n        \"rect_bottom_5\": {\n          \"end\": [\n            40.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_8\": {\n          \"end\": [\n            40.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_7\": {\n          \"end\": [\n            540.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_6\": {\n          \"end\": [\n            540.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-06 08:25:53\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}",
  "makerjs_svg": null
}
App.vue:694 Draw commands received: []
App.vue:696 MakerJS JSON received: {
  "models": {
    "50MM": {
      "paths": {
        "rect_bottom_9": {
          "end": [
            65.0,
            221.0
          ],
          "origin": [
            215.0,
            221.0...
VisualizationPanel.vue:1118 Processing makerjs JSON: {
  "models": {
    "50MM": {
      "paths": {
        "rect_bottom_9": {
          "end": [
            65.0,
            221.0
          ],
          "origin": [
            215.0,
            221.0...
VisualizationPanel.vue:1120 Converted makerjs JSON to 12 draw commands
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: {panelCommands: Array(4), topTools: Array(1), bottomTools: Array(0)}
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: {width: 500, height: 700, thickness: 18, cornerRadius: undefined, offsetX: 40, …}
OCJSCanvas.vue:154 Door parameters: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined, offsetX: 40, …}
OCJSCanvas.vue:155 Door size in mm: W=500 H=700 T=18
OCJSCanvas.vue:172 ⚙️ Sending to worker: {doorParamsMeters: {…}, topTools: Array(1), bottomTools: Array(0)}
ocjsService.ts:226 🔧 SWEEP OPERATION: Starting door processing with tools
ocjsService.ts:228 📐 Creating door body with params: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined, offsetX: 40, …}
ocjsWorker.ts:818 Worker received message: createDoorBody
ocjsWorker.ts:93 Creating box with dimensions: 0.5 0.7 0.018
ocjsWorker.ts:97 🚪 Door body dimensions: W=0.5m, H=0.7m, T=0.018m
ocjsWorker.ts:104 🚪 Door body centered at origin: X=[-0.25, 0.25], Y=[-0.35, 0.35], Z=[-0.009, 0.009]
ocjsWorker.ts:109 ✅ Door body cached with ID: door_1751779555192
ocjsWorker.ts:860 Worker completed: createDoorBody
OCJSCanvas.vue:592 OCJSCanvas mounted
ocjsService.ts:232 ✅ Door body created successfully
ocjsService.ts:239 🔍 All tool operations: ['20mm End Mill (cylindrical, ⌀20mm) - 4 commands']
ocjsService.ts:242 🔧 Creating positioned tool shapes for 1 tool operations...
ocjsService.ts:249 🔧 Processing 20mm End Mill with 4 commands
ocjsWorker.ts:818 Worker received message: createPositionedToolShapes
ocjsWorker.ts:223 🔧 Tool 20mm End Mill: diameter=20mm, radius=0.01m (10mm)
ocjsWorker.ts:225 🔧 Creating 4 positioned cylindrical tool shapes for 20mm End Mill
ocjsWorker.ts:291 🔧 Positioning line tool 0 at: X=-0.1500m, Y=0.0050m, Z=-0.2250m (from 140, 221 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:291 🔧 Positioning line tool 1 at: X=-0.2250m, Y=0.0050m, Z=-0.2750m (from 65, 171 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:291 🔧 Positioning line tool 2 at: X=-0.0750m, Y=0.0050m, Z=-0.2750m (from 215, 171 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:291 🔧 Positioning line tool 3 at: X=-0.1500m, Y=0.0050m, Z=-0.3250m (from 140, 121 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:321 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:860 Worker completed: createPositionedToolShapes
ocjsService.ts:263 ✅ Created 4 positioned shapes for 20mm End Mill
ocjsService.ts:274 🔧 Performing sweep operations with 4 positioned tool shapes...
ocjsWorker.ts:818 Worker received message: performSweepOperation
ocjsWorker.ts:486 🔧 Starting sweep operation: subtract
ocjsWorker.ts:505 🔧 Processing 4 tool geometries
ocjsWorker.ts:508 🔍 Starting CSG operations on door body
ocjsWorker.ts:530 🔧 Attempting to subtract tool 0...
ocjsWorker.ts:540 ✅ Tool 0 subtracted successfully
ocjsWorker.ts:544 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:530 🔧 Attempting to subtract tool 1...
ocjsWorker.ts:540 ✅ Tool 1 subtracted successfully
ocjsWorker.ts:544 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:530 🔧 Attempting to subtract tool 2...
ocjsWorker.ts:540 ✅ Tool 2 subtracted successfully
ocjsWorker.ts:544 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:530 🔧 Attempting to subtract tool 3...
ocjsWorker.ts:540 ✅ Tool 3 subtracted successfully
ocjsWorker.ts:544 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:575 ✅ Sweep operation completed, result cached with ID: result_1751779555244
ocjsWorker.ts:860 Worker completed: performSweepOperation
ocjsService.ts:281 ✅ Sweep operations completed: result_1751779555244
ocjsService.ts:284 🔧 Exporting final result to GLB...
ocjsWorker.ts:818 Worker received message: exportGLB
ocjsWorker.ts:592 🔧 Exporting to GLB...
ocjsWorker.ts:617 🔍 Exporting final shape to GLB format
ocjsWorker.ts:643 ✅ GLB export completed, size: 3232 bytes
ocjsWorker.ts:860 Worker completed: exportGLB
ocjsService.ts:286 ✅ Final GLB exported, size: 3232 bytes
OCJSCanvas.vue:181 ✅ Worker processing completed, GLB data size: 3232
OCJSCanvas.vue:195 Model URL created: blob:http://localhost:1420/0a237ba9-b44e-4779-b8f9-015e1a240a5f
OCJSCanvas.vue:233 ✅ Initializing Three.js scene
OCJSCanvas.vue:234 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:235 📐 Container dimensions: {width: 1920, height: 0, offsetWidth: 1920, offsetHeight: 0}
OCJSCanvas.vue:254 Container dimensions: {width: 1920, height: 600, aspect: 3.2}
OCJSCanvas.vue:270 Renderer created with size: 1920 x 600
OCJSCanvas.vue:271 Canvas element: <canvas data-engine=​"three.js r178" width=​"1920" height=​"600" style=​"display:​ block;​ width:​ 1920px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:272 Canvas style: display: block; width: 1920px; height: 600px;
OCJSCanvas.vue:307 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:319 🔄 Loading GLB model: blob:http://localhost:1420/0a237ba9-b44e-4779-b8f9-015e1a240a5f
OCJSCanvas.vue:387 Loading progress: 100%
OCJSCanvas.vue:326 GLB model loaded successfully
OCJSCanvas.vue:350 Model bounding box: {center: _Vector3, size: _Vector3, min: _Vector3, max: _Vector3}
OCJSCanvas.vue:362 Max dimension: 0.7
OCJSCanvas.vue:366 Camera distance: 2.0999999999999996
OCJSCanvas.vue:378 Model centered and camera adjusted
OCJSCanvas.vue:379 Camera position: _Vector3 {x: 2.0999999999999996, y: 1.575, z: 2.1}
OCJSCanvas.vue:380 Model position: _Vector3 {x: 0, y: 0, z: 0}
